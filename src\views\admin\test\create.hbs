<div class="header-container">
  <h1>Generate Test Message</h1>
</div>

<div class="form-container">


  <!-- Top row with Freelancer ID and Generate Button -->
  <div class="freelancer-generate-row">
    <div class="freelancer-input-container">
      <div class="form-div">
        <label for="freelancerInput">Freelancer URL or ID:</label>
        <input type="text" id="freelancerInput" class="form-control" placeholder="abc123 or https://upwork.com/freelancers/~abc123">
        <small class="form-text text-muted">Parsed ID: <span id="parsedId">-</span></small>
      </div>
    </div>
    <button id="generateBtn" class="btn btn-primary">Generate Message</button>
  </div>

  <!-- Generated Message Result - moved here -->
  <div id="resultContainer" style="display: none;" class="result-container">
    <h3>Generated Message:</h3>
    <div class="alert alert-success">Copied to clipboard!</div>
    <textarea id="generatedMessage" readonly class="form-control" rows="10"></textarea>
    <button id="copyBtn" class="btn btn-secondary mt-2">Copy Again</button>
  </div>

  <div class="form-div">
    <label for="group">Select Group:</label>
    <select id="group" class="form-control" required>
      <option value="">-- Select Group --</option>
      {{#each groups}}
        <option value="{{this.id}}" {{#if @first}}selected{{/if}}>{{this.name}}</option>
      {{/each}}
    </select>
  </div>

  <div class="form-div">
    <label for="template">Select Template:</label>
    <select id="template" class="form-control">
      <option value="">-- Select Template --</option>
      {{#each templates}}
        <option value="{{this.id}}" {{#if @first}}selected{{/if}}>{{this.template}}</option>
      {{/each}}
    </select>
  </div>

  <div class="form-div">
    <label for="templatePreview">Template Preview:</label>
    <textarea id="templatePreview" readonly class="form-control template-preview" rows="10"></textarea>
  </div>

  <div class="form-div">
    <label for="testName">Select Test:</label>
    <select id="testName" class="form-control" required>
      <option value="">-- Select Test --</option>
      {{#each tests}}
        <option value="{{this.name}}" data-test-id="{{this.id}}" {{#if @first}}selected{{/if}}>{{this.name}}</option>
      {{/each}}
    </select>
    <small>Select an existing test to generate a message</small>
  </div>

  <div class="form-div">
    <label>Test Questions:</label>
    <div id="questions-container">
      <!-- Questions will be added here dynamically -->
    </div>
    <button type="button" id="addQuestionBtn" class="btn btn-secondary mt-2" style="display: none;">Add Question</button>
  </div>

  <div class="form-div">
    <label>Tracking Configuration:</label>
    <div class="tracking-options">
      <div class="tracking-option">
        <input type="checkbox" id="trackFocusEvents" checked>
        <label for="trackFocusEvents">Track Focus Events</label>
      </div>
      <div class="tracking-option">
        <input type="checkbox" id="trackMouseClicks" checked>
        <label for="trackMouseClicks">Track Mouse Clicks</label>
      </div>
      <div class="tracking-option">
        <input type="checkbox" id="trackKeyboardPresses" checked>
        <label for="trackKeyboardPresses">Track Keyboard Presses</label>
      </div>
      <div class="tracking-option">
        <input type="checkbox" id="trackDeviceFingerprint" checked>
        <label for="trackDeviceFingerprint">Track Device Fingerprint</label>
      </div>
      <div class="tracking-option">
        <input type="checkbox" id="trackClipboardEvents" checked>
        <label for="trackClipboardEvents">Track Clipboard Events</label>
      </div>
      <div class="tracking-option">
        <input type="checkbox" id="trackAnswerChanges" checked>
        <label for="trackAnswerChanges">Track Answer Changes</label>
      </div>
      <div class="tracking-option">
        <input type="checkbox" id="trackPreSubmitDelay" checked>
        <label for="trackPreSubmitDelay">Track Pre-Submit Delay</label>
      </div>
      <div class="tracking-option">
        <input type="checkbox" id="trackTimeToFirstInteraction" checked>
        <label for="trackTimeToFirstInteraction">Track Time to First Interaction</label>
      </div>
    </div>
  </div>




</div>

<!-- Styles are now in main.css -->

<script>
document.addEventListener('DOMContentLoaded', () => {
  // Add a "Select All" / "Deselect All" toggle for tracking options
  const trackingOptions = document.querySelectorAll('.tracking-option input[type="checkbox"]');
  const trackingContainer = document.querySelector('.tracking-options');

  // Create toggle button
  const toggleButton = document.createElement('button');
  toggleButton.type = 'button';
  toggleButton.className = 'btn btn-sm btn-secondary toggle-tracking';
  toggleButton.textContent = 'Deselect All';
  toggleButton.style.marginBottom = '10px';

  // Insert before the tracking options
  trackingContainer.parentNode.insertBefore(toggleButton, trackingContainer);

  // Toggle functionality
  let allSelected = true;
  toggleButton.addEventListener('click', () => {
    allSelected = !allSelected;
    trackingOptions.forEach(option => {
      option.checked = allSelected;
    });
    toggleButton.textContent = allSelected ? 'Deselect All' : 'Select All';
  });
  const templateSelect = document.getElementById('template');
  const templatePreview = document.getElementById('templatePreview');
  const groupSelect = document.getElementById('group');
  const testNameSelect = document.getElementById('testName');
  const freelancerInput = document.getElementById('freelancerInput');
  const parsedIdSpan = document.getElementById('parsedId');
  const generateBtn = document.getElementById('generateBtn');
  const resultContainer = document.getElementById('resultContainer');
  const generatedMessage = document.getElementById('generatedMessage');
  const copyBtn = document.getElementById('copyBtn');
  const addQuestionBtn = document.getElementById('addQuestionBtn');
  const questionsContainer = document.getElementById('questions-container');

  // Track questions
  let questionCount = 0;
  const questions = [];
  let currentTestId = null; // Track currently selected test ID

  

  // Function to load test details
  const loadTestDetails = async (testId) => {
    try {
      const key = '{{key}}';
      const response = await fetch(`/admin/test/details/${testId}?key=${key}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch test details: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Load tracking configuration
        loadTrackingConfig(data.test.tracking_config);

        // Load questions
        loadQuestions(data.questions);

        console.log('Loaded test details:', data);
      } else {
        throw new Error(data.error || 'Failed to load test details');
      }
    } catch (error) {
      console.error('Error loading test details:', error);
      alert('Failed to load test details: ' + error.message);
    }
  };

  // Function to clear test details
  const clearTestDetails = () => {
    // Reset tracking configuration to defaults
    document.querySelectorAll('.tracking-option input[type="checkbox"]').forEach(checkbox => {
      checkbox.checked = true; // Default is all tracking enabled
      checkbox.disabled = false; // Enable checkboxes
    });
    const toggleButton = document.querySelector('.form-div .toggle-tracking');
    toggleButton.disabled = false;

    // Clear all questions
    questionsContainer.innerHTML = '';
    questionCount = 0;
  };

  // Function to set read-only mode for the form
  const setReadOnlyMode = (isReadOnly) => {
    // Disable/enable tracking configuration checkboxes
    document.querySelectorAll('.tracking-option input[type="checkbox"]').forEach(checkbox => {
      checkbox.disabled = isReadOnly;
    });
    const toggleButton = document.querySelector('.form-div .toggle-tracking');
    toggleButton.disabled = isReadOnly;
    console.log('disabled', toggleButton?.disabled);

    // Always hide Add Question button since we only work with existing tests
    addQuestionBtn.style.display = 'none';
  };

  // Function to load tracking configuration
  const loadTrackingConfig = (config) => {
    // Set tracking checkboxes based on config
    // Note: config stores disabled features as true, checkboxes show enabled features
    document.getElementById('trackFocusEvents').checked = !config.disableFocusEvents;
    document.getElementById('trackMouseClicks').checked = !config.disableMouseClickEvents;
    document.getElementById('trackKeyboardPresses').checked = !config.disableKeyboardPressEvents;
    document.getElementById('trackDeviceFingerprint').checked = !config.disableDeviceFingerprint;
    document.getElementById('trackClipboardEvents').checked = !config.disableClipboardEvents;
    document.getElementById('trackAnswerChanges').checked = !config.disableAnswerChangeEvents;
    document.getElementById('trackPreSubmitDelay').checked = !config.disablePreSubmitDelay;
    document.getElementById('trackTimeToFirstInteraction').checked = !config.disableTimeToFirstInteraction;
  };

  // Function to load questions
  const loadQuestions = (questionsData) => {
    // Clear existing questions
    questionsContainer.innerHTML = '';
    questionCount = 0;

    // Add each question
    questionsData.forEach(questionData => {
      addQuestionToForm(questionData);
    });
  };

  // Template selection
  templateSelect.addEventListener('change', (e) => {
    const selectedOption = e.target.options[e.target.selectedIndex];
    templatePreview.value = selectedOption.value ? selectedOption.text : '';
    updatePreview();
  });

  // Test selection - load test details when test is selected
  testNameSelect.addEventListener('change', async (e) => {
    if (e.target.value !== '') {
      // Load test details when test is selected
      const selectedOption = e.target.options[e.target.selectedIndex];
      const testId = selectedOption.getAttribute('data-test-id');
      if (testId) {
        currentTestId = parseInt(testId);
        await loadTestDetails(testId);

        // Make UI read-only when test is selected
        setReadOnlyMode(true);
      }
    } else {
      // Clear loaded test details when no test is selected
      currentTestId = null;
      clearTestDetails();

      // Enable editing mode when no test is selected
      setReadOnlyMode(false);
    }
  });

  // Auto-select the first template, group, and test (latest created) and trigger change events
  if (templateSelect.options.length > 1) {
    templateSelect.selectedIndex = 1; // Skip the "-- Select Template --" option
    // Manually populate the template preview
    const selectedOption = templateSelect.options[templateSelect.selectedIndex];
    templatePreview.value = selectedOption.text;
    templateSelect.dispatchEvent(new Event('change'));
  }

  if (groupSelect.options.length > 1) {
    groupSelect.selectedIndex = 1; // Skip the "-- Select Group --" option
  }

  if (testNameSelect.options.length > 1) {
    testNameSelect.selectedIndex = 1; // Skip the "-- Select Test --" option
    currentTestId = testNameSelect.options[testNameSelect.selectedIndex].dataset.testId;
    testNameSelect.dispatchEvent(new Event('change'));
  }

  // Parse freelancer ID
  freelancerInput.addEventListener('input', (e) => {
    let parsed = e.target.value;
    if (e.target.value.includes('upwork.com')) {
      const match = e.target.value.match(/~([a-zA-Z0-9]+)/);
      parsed = match ? match[1] : e.target.value;
    }
    parsedIdSpan.textContent = parsed;
    updatePreview();
  });

  // Update preview with parsed ID
  const updatePreview = () => {
    const template = templatePreview.value;
    const parsedId = parsedIdSpan.textContent;
    if (template && parsedId && parsedId !== '-') {
      templatePreview.value = template.replace(/{link}/g, `[ID: ${parsedId}]`);
    }
  };

  // Generate test
  generateBtn.addEventListener('click', async () => {
    const templateId = templateSelect.value;
    const groupId = groupSelect.value;
    const selectedTestName = testNameSelect.value;
    const freelancerInputValue = freelancerInput.value;

    if (!templateId || !groupId || !selectedTestName || !freelancerInputValue) {
      alert('Please fill all required fields (Template, Test, Group, and Freelancer)');
      return;
    }

    // Get test ID from selected test
    const selectedOption = testNameSelect.options[testNameSelect.selectedIndex];
    const testId = selectedOption.getAttribute('data-test-id');

    if (!testId) {
      alert('Please select a valid test');
      return;
    }

    try {
      // Collect tracking configuration
      const trackingConfig = {
        disableFocusEvents: !document.getElementById('trackFocusEvents').checked,
        disableMouseClickEvents: !document.getElementById('trackMouseClicks').checked,
        disableKeyboardPressEvents: !document.getElementById('trackKeyboardPresses').checked,
        disableDeviceFingerprint: !document.getElementById('trackDeviceFingerprint').checked,
        disableClipboardEvents: !document.getElementById('trackClipboardEvents').checked,
        disableAnswerChangeEvents: !document.getElementById('trackAnswerChanges').checked,
        disablePreSubmitDelay: !document.getElementById('trackPreSubmitDelay').checked,
        disableTimeToFirstInteraction: !document.getElementById('trackTimeToFirstInteraction').checked
      };

      // Collect questions
      const questions = [];
      document.querySelectorAll('.question-item').forEach(questionItem => {
        const questionText = questionItem.querySelector('.question-text').value.trim();
        const answerType = questionItem.querySelector('.answer-type').value;
        const answerHtml = questionItem.querySelector('.answer-html').value.trim();
        const correctAnswerRaw = questionItem.querySelector('.correct-answer').value.trim();

        if (questionText) {
          // Process correct answer - convert "First OR Second" back to ["First", "Second"] if needed
          let correctAnswer = correctAnswerRaw;
          if (correctAnswerRaw && correctAnswerRaw.includes(' OR ')) {
            // Split by " OR " and create JSON array
            const answers = correctAnswerRaw.split(' OR ').map(answer => answer.trim()).filter(answer => answer);
            if (answers.length > 1) {
              correctAnswer = JSON.stringify(answers);
            }
          }

          questions.push({
            question: questionText,
            answer_type: answerType,
            answer_html: answerHtml,
            correct: correctAnswer
          });
        }
      });

      const key = '{{key}}'; // Get the key from the template context

      // Prepare request body - always use test_id since we only work with existing tests
      const requestBody = {
        template_id: templateId,
        group_id: groupId,
        freelancer_input: freelancerInputValue,
        tracking_config: trackingConfig,
        questions: questions,
        test_id: parseInt(testId)
      };

      const response = await fetch(`/admin/test/create-link?key=${key}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        generatedMessage.value = data.message;
        resultContainer.style.display = 'block';

        // Copy to clipboard
        navigator.clipboard.writeText(data.message).then(() => {
          document.querySelector('.alert').style.display = 'block';
          setTimeout(() => {
            document.querySelector('.alert').style.display = 'none';
          }, 3000);
        }).catch(error => {
          console.error('Failed to copy to clipboard:', error);
          alert('Generated message is ready but could not be copied to clipboard automatically.');
        });
      } else {
        alert('Error: ' + (data.error || 'Unknown error occurred'));
      }
    } catch (error) {
      alert('Error: ' + error.message);
    }
  });

  // Copy again button
  copyBtn.addEventListener('click', () => {
    navigator.clipboard.writeText(generatedMessage.value).then(() => {
      document.querySelector('.alert').style.display = 'block';
      setTimeout(() => {
        document.querySelector('.alert').style.display = 'none';
      }, 3000);
    }).catch(error => {
      console.error('Failed to copy to clipboard:', error);
      alert('Failed to copy to clipboard. Please try again.');
    });
  });

  // Function to add question to form (used for both new questions and loading existing ones)
  const addQuestionToForm = (questionData = null) => {
    questionCount++;
    const questionId = `question-${questionCount}`;

    // Determine if this is a read-only question (loading existing question)
    const isReadOnly = questionData !== null;

    const questionDiv = document.createElement('div');
    questionDiv.className = `question-item mb-3 p-3 border rounded${isReadOnly ? ' readonly' : ''}`;
    questionDiv.id = questionId;

    // Set values from questionData if provided, otherwise use defaults
    const questionText = questionData ? questionData.question : '';
    const answerType = questionData ? questionData.answer_type : 'textarea';
    const answerHtml = questionData ? questionData.answer_html : '';

    // Handle correct answer - parse JSON arrays and format properly
    let correctAnswer = '';
    if (questionData && questionData.correct) {
      try {
        // Try to parse as JSON first (for arrays like ["First", "Second"])
        const parsed = JSON.parse(questionData.correct);
        if (Array.isArray(parsed)) {
          // Join array elements with " OR " for display
          correctAnswer = parsed.join(' OR ');
        } else {
          correctAnswer = questionData.correct;
        }
      } catch (e) {
        // If not valid JSON, use as-is (for simple strings)
        correctAnswer = questionData.correct;
      }
    }

    questionDiv.innerHTML = `
      <div class="form-group mb-2">
        <label for="${questionId}-text">Question Text:</label>
        <textarea id="${questionId}-text" class="form-control question-text" rows="2" placeholder="Enter question text" ${isReadOnly ? 'readonly' : ''}>${questionText}</textarea>
      </div>
      <div class="form-group mb-2">
        <label for="${questionId}-type">Answer Type:</label>
        <select id="${questionId}-type" class="form-control answer-type" ${isReadOnly ? 'disabled' : ''}>
          <option value="textarea" ${answerType === 'textarea' ? 'selected' : ''}>Text Area</option>
          <option value="radiobutton" ${answerType === 'radiobutton' ? 'selected' : ''}>Radio Buttons</option>
          <option value="multiinput" ${answerType === 'multiinput' ? 'selected' : ''}>Multiple Choice</option>
          <option value="multiTextInput" ${answerType === 'multiTextInput' ? 'selected' : ''}>Multiple Text Inputs</option>
        </select>
      </div>
      <div class="form-group mb-2 answer-html-container">
        <label for="${questionId}-html">Answer HTML:</label>
        <textarea id="${questionId}-html" class="form-control answer-html" rows="3" placeholder="Enter HTML for answer options" ${isReadOnly ? 'readonly' : ''}>${answerHtml}</textarea>
        ${isReadOnly ? '' : `<small class="form-text text-muted">
          For textarea: <code>&lt;textarea name="answer" class="form-control"&gt;&lt;/textarea&gt;</code><br>
          For radiobutton: <code>&lt;div&gt;&lt;input type="radio" name="answer" value="option1"&gt; Option 1&lt;/div&gt;</code><br>
          For multiinput: <code>&lt;div&gt;&lt;input type="checkbox" name="answer" value="option1"&gt; Option 1&lt;/div&gt;</code><br>
          For multiTextInput: <code>&lt;div&gt;&lt;input type="text" name="answer-1" placeholder="Answer 1"&gt;&lt;/div&gt;</code>
        </small>`}
      </div>
      <div class="form-group mb-2">
        <label for="${questionId}-correct">Correct Answer:</label>
        <input type="text" id="${questionId}-correct" class="form-control correct-answer" placeholder="Enter correct answer or leave blank for essay questions" value="${correctAnswer}" ${isReadOnly ? 'readonly' : ''}>
        ${isReadOnly ? '' : `<small class="form-text text-muted">
          For multiple correct answers, separate with OR (e.g., "answer1 OR answer2")
        </small>`}
      </div>
      ${isReadOnly ? '' : `<button type="button" class="btn btn-danger btn-sm remove-question" data-question-id="${questionId}">Remove Question</button>`}
    `;

    questionsContainer.appendChild(questionDiv);

    // Only add event listeners for editable questions
    if (!isReadOnly) {
      // Add event listener for remove button
      const removeButton = questionDiv.querySelector('.remove-question');
      if (removeButton) {
        removeButton.addEventListener('click', (e) => {
          const questionId = e.target.getAttribute('data-question-id');
          document.getElementById(questionId).remove();
        });
      }

      // Add event listener for answer type change
      questionDiv.querySelector('.answer-type').addEventListener('change', (e) => {
        const answerType = e.target.value;
        const answerHtmlTextarea = questionDiv.querySelector('.answer-html');

        // Only set default HTML if the field is empty (for new questions)
        if (!answerHtmlTextarea.value.trim()) {
          if (answerType === 'textarea') {
            answerHtmlTextarea.value = '<textarea name="answer" class="form-control"></textarea>';
          } else if (answerType === 'radiobutton') {
            answerHtmlTextarea.value = '<div><input type="radio" name="answer" value="option1"> Option 1</div>\n<div><input type="radio" name="answer" value="option2"> Option 2</div>';
          } else if (answerType === 'multiinput') {
            answerHtmlTextarea.value = '<div><input type="checkbox" name="answer" value="option1"> Option 1</div>\n<div><input type="checkbox" name="answer" value="option2"> Option 2</div>';
          } else if (answerType === 'multiTextInput') {
            answerHtmlTextarea.value = '<div><input type="text" name="answer-1" placeholder="Answer 1"></div>\n<div><input type="text" name="answer-2" placeholder="Answer 2"></div>';
          }
        }
      });

      // Only trigger change event for new questions (not when loading existing ones)
      if (!questionData) {
        questionDiv.querySelector('.answer-type').dispatchEvent(new Event('change'));
      }
    }
  };
});
</script>
